import { useState, useEffect } from "react";
import { getAccommodationStats } from "@/lib/accommodations";
import { getActivityStats } from "@/lib/activities";
import { getBookingStatistics } from "@/lib/bookings";
import { supabase } from "@/lib/supabase";
import type { BookingStatistics } from "@/types/booking";
import type { ActivityStats } from "@/types/activity";

interface AccommodationStats {
  total: number;
  published: number;
  draft: number;
  unpublished: number;
  featured: number;
  by_type: Record<string, number>;
}

interface RecentActivity {
  id: string;
  type: "booking" | "accommodation" | "activity";
  action: string;
  description: string;
  timestamp: string;
  status: "success" | "info" | "warning";
}

interface DashboardStats {
  accommodations: AccommodationStats;
  activities: ActivityStats;
  bookings: BookingStatistics;
}

interface DashboardData {
  stats: DashboardStats | null;
  recentActivities: RecentActivity[];
  loading: boolean;
  error: string | null;
  refreshData: () => Promise<void>;
}

// Function to fetch recent activities from database
const getRecentActivities = async (): Promise<RecentActivity[]> => {
  try {
    // Get recent bookings
    const { data: recentBookings } = await supabase
      .from("bookings")
      .select("id, guest_full_name, created_at, status")
      .order("created_at", { ascending: false })
      .limit(3);

    // Get recent accommodations
    const { data: recentAccommodations } = await supabase
      .from("accommodations")
      .select("id, name, created_at, updated_at, status")
      .order("updated_at", { ascending: false })
      .limit(3);

    // Get recent activities
    const { data: recentActivitiesData } = await supabase
      .from("activities")
      .select("id, title, created_at, updated_at, status")
      .order("updated_at", { ascending: false })
      .limit(3);

    const activities: RecentActivity[] = [];

    // Process bookings
    if (recentBookings) {
      recentBookings.forEach((booking) => {
        activities.push({
          id: `booking-${booking.id}`,
          type: "booking",
          action: "New booking received",
          description: `Booking from ${booking.guest_full_name}`,
          timestamp: booking.created_at,
          status: booking.status === "confirmed" ? "success" : "info",
        });
      });
    }

    // Process accommodations
    if (recentAccommodations) {
      recentAccommodations.forEach((accommodation) => {
        const isNew =
          new Date(accommodation.created_at).getTime() ===
          new Date(accommodation.updated_at).getTime();
        activities.push({
          id: `accommodation-${accommodation.id}`,
          type: "accommodation",
          action: isNew ? "New accommodation added" : "Accommodation updated",
          description: accommodation.name,
          timestamp: accommodation.updated_at,
          status: accommodation.status === "published" ? "success" : "info",
        });
      });
    }

    // Process activities
    if (recentActivitiesData) {
      recentActivitiesData.forEach((activity) => {
        const isNew =
          new Date(activity.created_at).getTime() ===
          new Date(activity.updated_at).getTime();
        activities.push({
          id: `activity-${activity.id}`,
          type: "activity",
          action: isNew ? "New activity added" : "Activity updated",
          description: activity.title,
          timestamp: activity.updated_at,
          status: activity.status === "published" ? "success" : "info",
        });
      });
    }

    // Sort by timestamp and return top 5
    return activities
      .sort(
        (a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      )
      .slice(0, 5);
  } catch (error) {
    console.error("Error fetching recent activities:", error);
    return [];
  }
};

export const useDashboardData = (): DashboardData => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>(
    []
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load all statistics and recent activities in parallel
      const [
        accommodationStats,
        activityStats,
        bookingStatsResponse,
        activities,
      ] = await Promise.all([
        getAccommodationStats(),
        getActivityStats(),
        getBookingStatistics(),
        getRecentActivities(),
      ]);

      // Handle booking statistics response format
      const bookingStats = bookingStatsResponse.success
        ? bookingStatsResponse.data
        : {
            total_bookings: 0,
            confirmed_bookings: 0,
            pending_bookings: 0,
            cancelled_bookings: 0,
            completed_bookings: 0,
            total_revenue: 0,
            paid_revenue: 0,
            pending_revenue: 0,
            average_booking_value: 0,
            total_guests: 0,
          };

      setStats({
        accommodations: accommodationStats,
        activities: activityStats,
        bookings: bookingStats,
      });

      setRecentActivities(activities);
    } catch (err) {
      console.error("Error loading dashboard data:", err);
      setError(
        err instanceof Error ? err.message : "Failed to load dashboard data"
      );
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    await loadDashboardData();
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  return {
    stats,
    recentActivities,
    loading,
    error,
    refreshData,
  };
};

// Helper functions to extract specific metrics
export const getDashboardMetrics = (stats: DashboardStats | null) => {
  if (!stats) {
    return {
      totalAccommodations: 0,
      totalActivities: 0,
      totalBookings: 0,
      totalRevenue: 0,
    };
  }

  return {
    totalAccommodations: stats.accommodations?.total || 0,
    totalActivities: stats.activities?.total_activities || 0,
    totalBookings: stats.bookings?.total_bookings || 0,
    totalRevenue: stats.bookings?.total_revenue || 0,
  };
};

export const getSystemStatus = (stats: DashboardStats | null) => {
  if (!stats) {
    return {
      status: "Loading",
      description: "Checking system status...",
      color: "text-gray-600",
    };
  }

  // Simple system health check based on data availability
  const hasData =
    (stats.accommodations?.total || 0) > 0 ||
    (stats.activities?.total_activities || 0) > 0;

  return {
    status: hasData ? "Online" : "No Data",
    description: hasData ? "All systems operational" : "No content available",
    color: hasData ? "text-emerald-600" : "text-amber-600",
  };
};
